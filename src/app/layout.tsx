import type { Metadata } from 'next';

import { AntdRegistry } from '@ant-design/nextjs-registry';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import './globals.css';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';

const geistSans = Geist({
    variable: '--font-geist-sans',
    subsets: ['latin'],
});

const geistMono = Geist_Mono({
    variable: '--font-geist-mono',
    subsets: ['latin'],
});

export const metadata: Metadata = {
    title: 'Tank admin web app',
    description: 'Generated by create next app',
    icons: 'biohazard.svg',
};

const queryClient = new QueryClient();

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body className={`${geistSans.variable} ${geistMono.variable}`}>
                <AntdRegistry>
                    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
                </AntdRegistry>
            </body>
        </html>
    );
}
